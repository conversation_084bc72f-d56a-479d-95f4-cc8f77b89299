import * as XLSX from 'xlsx';

export const readExcelFile = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        
        const result = {
          fileName: file.name,
          fileSize: file.size,
          sheets: [],
          sheetNames: workbook.SheetNames
        };

        // Procesar cada hoja
        workbook.SheetNames.forEach(sheetName => {
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
            header: 1,
            defval: '',
            raw: false
          });
          
          // Obtener el rango de la hoja
          const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
          
          const sheetInfo = {
            name: sheetName,
            data: jsonData,
            rowCount: jsonData.length,
            columnCount: range.e.c + 1,
            range: worksheet['!ref'] || 'A1:A1'
          };

          result.sheets.push(sheetInfo);
        });

        resolve(result);
      } catch (error) {
        reject(new Error(`Error al leer el archivo: ${error.message}`));
      }
    };

    reader.onerror = () => {
      reject(new Error('Error al leer el archivo'));
    };

    reader.readAsArrayBuffer(file);
  });
};

export const analyzeData = (data) => {
  if (!data || data.length === 0) {
    return {
      totalRows: 0,
      totalColumns: 0,
      hasHeaders: false,
      columnTypes: [],
      summary: {}
    };
  }

  const totalRows = data.length;
  const totalColumns = data[0] ? data[0].length : 0;
  
  // Detectar si la primera fila son encabezados
  const hasHeaders = detectHeaders(data);
  
  // Analizar tipos de columnas
  const columnTypes = analyzeColumnTypes(data, hasHeaders);
  
  // Generar resumen estadístico
  const summary = generateSummary(data, hasHeaders, columnTypes);

  return {
    totalRows,
    totalColumns,
    hasHeaders,
    columnTypes,
    summary
  };
};

const detectHeaders = (data) => {
  if (data.length < 2) return false;
  
  const firstRow = data[0];
  const secondRow = data[1];
  
  // Si la primera fila tiene strings y la segunda números, probablemente son headers
  let stringCount = 0;
  let numberCount = 0;
  
  firstRow.forEach((cell, index) => {
    if (typeof cell === 'string' && cell.trim() !== '') {
      stringCount++;
    }
    if (secondRow[index] && !isNaN(parseFloat(secondRow[index]))) {
      numberCount++;
    }
  });
  
  return stringCount > firstRow.length * 0.5;
};

const analyzeColumnTypes = (data, hasHeaders) => {
  if (data.length === 0) return [];
  
  const startRow = hasHeaders ? 1 : 0;
  const columnCount = data[0].length;
  const types = [];
  
  for (let col = 0; col < columnCount; col++) {
    const columnData = data.slice(startRow).map(row => row[col]).filter(cell => cell !== '');
    
    if (columnData.length === 0) {
      types.push('empty');
      continue;
    }
    
    let numberCount = 0;
    let dateCount = 0;
    let stringCount = 0;
    
    columnData.forEach(cell => {
      if (!isNaN(parseFloat(cell)) && isFinite(cell)) {
        numberCount++;
      } else if (isValidDate(cell)) {
        dateCount++;
      } else {
        stringCount++;
      }
    });
    
    const total = columnData.length;
    if (numberCount / total > 0.8) {
      types.push('number');
    } else if (dateCount / total > 0.8) {
      types.push('date');
    } else {
      types.push('text');
    }
  }
  
  return types;
};

const isValidDate = (value) => {
  const date = new Date(value);
  return date instanceof Date && !isNaN(date);
};

const generateSummary = (data, hasHeaders, columnTypes) => {
  const startRow = hasHeaders ? 1 : 0;
  const summary = {};
  
  columnTypes.forEach((type, index) => {
    const columnName = hasHeaders ? data[0][index] || `Columna ${index + 1}` : `Columna ${index + 1}`;
    const columnData = data.slice(startRow).map(row => row[index]).filter(cell => cell !== '');
    
    summary[columnName] = {
      type,
      count: columnData.length,
      emptyCount: data.slice(startRow).length - columnData.length
    };
    
    if (type === 'number') {
      const numbers = columnData.map(cell => parseFloat(cell)).filter(num => !isNaN(num));
      if (numbers.length > 0) {
        summary[columnName].min = Math.min(...numbers);
        summary[columnName].max = Math.max(...numbers);
        summary[columnName].avg = numbers.reduce((a, b) => a + b, 0) / numbers.length;
        summary[columnName].sum = numbers.reduce((a, b) => a + b, 0);
      }
    }
  });
  
  return summary;
};

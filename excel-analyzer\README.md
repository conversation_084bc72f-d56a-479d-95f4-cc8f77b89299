# 📊 Analizador de Excel

Una aplicación web moderna y fácil de usar para cargar, analizar y visualizar archivos Excel (.xlsx, .xls) directamente en tu navegador.

## ✨ Características

- **🔄 Carga de archivos**: Interfaz drag-and-drop intuitiva para cargar archivos Excel
- **📋 Múltiples hojas**: Soporte completo para archivos con múltiples hojas de cálculo
- **🔍 Búsqueda y filtrado**: Busca datos específicos en tiempo real
- **📊 Análisis automático**: Estadísticas automáticas y análisis de tipos de datos
- **🗂️ Ordenamiento**: Ordena columnas de forma ascendente o descendente
- **📄 Paginación**: Navegación eficiente para archivos grandes
- **💾 Exportación**: Exporta datos filtrados a formato CSV
- **📱 Responsive**: Diseño adaptable para dispositivos móviles
- **🎨 Interfaz moderna**: UI elegante con gradientes y efectos visuales

## 🚀 Tecnologías utilizadas

- **React 18** - Biblioteca de interfaz de usuario
- **Vite** - Herramienta de construcción rápida
- **XLSX** - Librería para leer archivos Excel
- **React Dropzone** - Componente de carga de archivos
- **Lucide React** - Iconos modernos
- **CSS3** - Estilos modernos con gradientes y animaciones

## 📦 Instalación

1. Clona o descarga este proyecto
2. Instala las dependencias:
```bash
npm install
```

3. Inicia el servidor de desarrollo:
```bash
npm run dev
```

4. Abre tu navegador en `http://localhost:5173`

## 🎯 Cómo usar

1. **Cargar archivo**: Arrastra y suelta un archivo Excel o haz clic para seleccionarlo
2. **Explorar datos**: Navega entre las diferentes hojas si el archivo tiene múltiples pestañas
3. **Buscar**: Usa la barra de búsqueda para encontrar datos específicos
4. **Analizar**: Activa el panel de análisis para ver estadísticas automáticas
5. **Ordenar**: Haz clic en los encabezados de columna para ordenar los datos
6. **Exportar**: Descarga los datos filtrados en formato CSV

## 📁 Estructura del proyecto

```
excel-analyzer/
├── src/
│   ├── components/
│   │   ├── FileUploader.jsx    # Componente de carga de archivos
│   │   └── DataViewer.jsx      # Visualizador de datos
│   ├── utils/
│   │   └── excelReader.js      # Utilidades para leer Excel
│   ├── App.jsx                 # Componente principal
│   ├── App.css                 # Estilos principales
│   └── main.jsx               # Punto de entrada
├── public/                     # Archivos estáticos
├── datos-prueba.xlsx          # Archivo Excel de ejemplo
└── create-test-excel.js       # Script para crear archivos de prueba
```

## 🧪 Archivo de prueba

El proyecto incluye un archivo Excel de ejemplo (`datos-prueba.xlsx`) con datos de empleados y ventas para que puedas probar todas las funcionalidades inmediatamente.

## 🔧 Scripts disponibles

- `npm run dev` - Inicia el servidor de desarrollo
- `npm run build` - Construye la aplicación para producción
- `npm run preview` - Previsualiza la construcción de producción
- `node create-test-excel.js` - Genera un nuevo archivo Excel de prueba

## 🌟 Funcionalidades destacadas

### Análisis automático de datos
- Detección automática de tipos de datos (texto, números, fechas)
- Estadísticas básicas para columnas numéricas (min, max, promedio, suma)
- Conteo de celdas vacías y totales

### Interfaz intuitiva
- Diseño moderno con gradientes y efectos visuales
- Indicadores visuales para drag-and-drop
- Paginación inteligente para archivos grandes
- Responsive design para móviles y tablets

### Rendimiento optimizado
- Carga asíncrona de archivos
- Paginación para manejar grandes volúmenes de datos
- Filtrado y búsqueda en tiempo real

## 🤝 Contribuir

¡Las contribuciones son bienvenidas! Si tienes ideas para mejorar la aplicación, no dudes en crear un issue o pull request.

## 📄 Licencia

Este proyecto está bajo la Licencia MIT.

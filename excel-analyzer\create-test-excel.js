// Script para crear un archivo Excel de prueba
import * as XLSX from 'xlsx';

// Crear datos de prueba
const testData = [
  ['Nombre', 'Edad', 'Sal<PERSON>', 'Fecha Ingreso', 'Departamento', 'Activo'],
  ['<PERSON>', 28, 45000, '2022-01-15', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'],
  ['<PERSON>', 32, 52000, '2021-03-20', 'Marketing', 'Sí'],
  ['<PERSON>', 45, 68000, '2019-07-10', 'IT', 'Sí'],
  ['<PERSON>', 29, 48000, '2022-05-08', 'Recursos Humanos', 'S<PERSON>'],
  ['<PERSON>', 38, 55000, '2020-11-12', 'Fin<PERSON><PERSON>', 'No'],
  ['<PERSON>', 26, 42000, '2023-02-14', 'Ventas', 'S<PERSON>'],
  ['<PERSON>', 41, 62000, '2018-09-25', 'IT', '<PERSON><PERSON>'],
  ['<PERSON>', 35, 50000, '2021-08-30', 'Marketing', 'S<PERSON>'],
  ['<PERSON>', 33, 47000, '2022-12-05', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'],
  ['<PERSON>', 27, 44000, '2023-04-18', 'Recursos Humanos', 'Sí']
];

const salesData = [
  ['Producto', 'Cantidad', 'Precio Unitario', 'Total', 'Mes'],
  ['Laptop', 15, 800, 12000, 'Enero'],
  ['Mouse', 50, 25, 1250, 'Enero'],
  ['Teclado', 30, 45, 1350, 'Enero'],
  ['Monitor', 20, 300, 6000, 'Febrero'],
  ['Laptop', 18, 800, 14400, 'Febrero'],
  ['Mouse', 45, 25, 1125, 'Febrero'],
  ['Impresora', 8, 200, 1600, 'Marzo'],
  ['Teclado', 25, 45, 1125, 'Marzo'],
  ['Monitor', 22, 300, 6600, 'Marzo']
];

// Crear un nuevo libro de trabajo
const workbook = XLSX.utils.book_new();

// Crear hojas de trabajo
const employeesSheet = XLSX.utils.aoa_to_sheet(testData);
const salesSheet = XLSX.utils.aoa_to_sheet(salesData);

// Agregar las hojas al libro
XLSX.utils.book_append_sheet(workbook, employeesSheet, 'Empleados');
XLSX.utils.book_append_sheet(workbook, salesSheet, 'Ventas');

// Guardar el archivo
XLSX.writeFile(workbook, 'datos-prueba.xlsx');

console.log('Archivo Excel de prueba creado: datos-prueba.xlsx');

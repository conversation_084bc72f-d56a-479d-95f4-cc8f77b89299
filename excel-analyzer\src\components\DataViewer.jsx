import React, { useState, useMemo } from 'react';
import { ChevronDown, ChevronUp, Search, Filter, Download, BarChart3 } from 'lucide-react';

const DataViewer = ({ excelData }) => {
  const [activeSheet, setActiveSheet] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortColumn, setSortColumn] = useState(null);
  const [sortDirection, setSortDirection] = useState('asc');
  const [showAnalysis, setShowAnalysis] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 50;

  const currentSheet = excelData.sheets[activeSheet];
  const analysis = useMemo(() => {
    if (!currentSheet) return null;
    return analyzeSheetData(currentSheet.data);
  }, [currentSheet]);

  // Filtrar y ordenar datos
  const filteredData = useMemo(() => {
    if (!currentSheet || !currentSheet.data) return [];
    
    let data = [...currentSheet.data];
    
    // Aplicar búsqueda
    if (searchTerm) {
      data = data.filter(row => 
        row.some(cell => 
          cell && cell.toString().toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }
    
    // Aplicar ordenamiento
    if (sortColumn !== null) {
      data.sort((a, b) => {
        const aVal = a[sortColumn] || '';
        const bVal = b[sortColumn] || '';
        
        const comparison = aVal.toString().localeCompare(bVal.toString(), undefined, { numeric: true });
        return sortDirection === 'asc' ? comparison : -comparison;
      });
    }
    
    return data;
  }, [currentSheet, searchTerm, sortColumn, sortDirection]);

  // Paginación
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * rowsPerPage;
    return filteredData.slice(startIndex, startIndex + rowsPerPage);
  }, [filteredData, currentPage]);

  const totalPages = Math.ceil(filteredData.length / rowsPerPage);

  const handleSort = (columnIndex) => {
    if (sortColumn === columnIndex) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(columnIndex);
      setSortDirection('asc');
    }
    setCurrentPage(1);
  };

  const exportToCSV = () => {
    if (!currentSheet) return;
    
    const csvContent = currentSheet.data.map(row => 
      row.map(cell => `"${cell || ''}"`).join(',')
    ).join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${currentSheet.name}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (!excelData || !excelData.sheets.length) {
    return <div className="no-data">No hay datos para mostrar</div>;
  }

  return (
    <div className="data-viewer">
      {/* Header con información del archivo */}
      <div className="file-info">
        <h2>{excelData.fileName}</h2>
        <div className="file-stats">
          <span>Tamaño: {(excelData.fileSize / 1024).toFixed(2)} KB</span>
          <span>Hojas: {excelData.sheets.length}</span>
        </div>
      </div>

      {/* Pestañas de hojas */}
      {excelData.sheets.length > 1 && (
        <div className="sheet-tabs">
          {excelData.sheets.map((sheet, index) => (
            <button
              key={index}
              className={`sheet-tab ${index === activeSheet ? 'active' : ''}`}
              onClick={() => {
                setActiveSheet(index);
                setCurrentPage(1);
                setSearchTerm('');
                setSortColumn(null);
              }}
            >
              {sheet.name} ({sheet.rowCount} filas)
            </button>
          ))}
        </div>
      )}

      {/* Controles */}
      <div className="controls">
        <div className="search-filter">
          <div className="search-box">
            <Search size={16} />
            <input
              type="text"
              placeholder="Buscar en los datos..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1);
              }}
            />
          </div>
          <button 
            className="analysis-toggle"
            onClick={() => setShowAnalysis(!showAnalysis)}
          >
            <BarChart3 size={16} />
            {showAnalysis ? 'Ocultar' : 'Mostrar'} Análisis
          </button>
          <button className="export-btn" onClick={exportToCSV}>
            <Download size={16} />
            Exportar CSV
          </button>
        </div>
      </div>

      {/* Panel de análisis */}
      {showAnalysis && analysis && (
        <div className="analysis-panel">
          <h3>Análisis de Datos</h3>
          <div className="analysis-grid">
            <div className="stat-card">
              <h4>Filas totales</h4>
              <span className="stat-value">{analysis.totalRows}</span>
            </div>
            <div className="stat-card">
              <h4>Columnas</h4>
              <span className="stat-value">{analysis.totalColumns}</span>
            </div>
            <div className="stat-card">
              <h4>Celdas vacías</h4>
              <span className="stat-value">{analysis.emptyCells}</span>
            </div>
            <div className="stat-card">
              <h4>Tipos de datos</h4>
              <div className="data-types">
                {Object.entries(analysis.columnTypes).map(([type, count]) => (
                  <span key={type} className="type-badge">{type}: {count}</span>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabla de datos */}
      <div className="data-table-container">
        <table className="data-table">
          <thead>
            <tr>
              {currentSheet.data[0] && currentSheet.data[0].map((header, index) => (
                <th key={index} onClick={() => handleSort(index)}>
                  <div className="header-content">
                    <span>{header || `Columna ${index + 1}`}</span>
                    {sortColumn === index && (
                      sortDirection === 'asc' ? <ChevronUp size={16} /> : <ChevronDown size={16} />
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {paginatedData.slice(1).map((row, rowIndex) => (
              <tr key={rowIndex}>
                {row.map((cell, cellIndex) => (
                  <td key={cellIndex}>{cell || ''}</td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Paginación */}
      {totalPages > 1 && (
        <div className="pagination">
          <button 
            disabled={currentPage === 1}
            onClick={() => setCurrentPage(currentPage - 1)}
          >
            Anterior
          </button>
          <span>Página {currentPage} de {totalPages}</span>
          <button 
            disabled={currentPage === totalPages}
            onClick={() => setCurrentPage(currentPage + 1)}
          >
            Siguiente
          </button>
        </div>
      )}

      {/* Información de resultados */}
      <div className="results-info">
        Mostrando {Math.min(rowsPerPage, filteredData.length - 1)} de {filteredData.length - 1} filas
        {searchTerm && ` (filtrado de ${currentSheet.data.length - 1} total)`}
      </div>
    </div>
  );
};

// Función auxiliar para analizar datos de la hoja
const analyzeSheetData = (data) => {
  if (!data || data.length === 0) return null;

  const totalRows = data.length;
  const totalColumns = data[0] ? data[0].length : 0;
  let emptyCells = 0;
  const columnTypes = { text: 0, number: 0, date: 0, empty: 0 };

  data.forEach(row => {
    row.forEach(cell => {
      if (!cell || cell === '') {
        emptyCells++;
        columnTypes.empty++;
      } else if (!isNaN(parseFloat(cell)) && isFinite(cell)) {
        columnTypes.number++;
      } else if (isValidDate(cell)) {
        columnTypes.date++;
      } else {
        columnTypes.text++;
      }
    });
  });

  return {
    totalRows,
    totalColumns,
    emptyCells,
    columnTypes
  };
};

const isValidDate = (value) => {
  const date = new Date(value);
  return date instanceof Date && !isNaN(date);
};

export default DataViewer;

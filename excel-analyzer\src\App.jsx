import { useState } from 'react'
import FileUploader from './components/FileUploader'
import DataViewer from './components/DataViewer'
import { readExcelFile } from './utils/excelReader'
import './App.css'

function App() {
  const [excelData, setExcelData] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)

  const handleFileUpload = async (file) => {
    setIsLoading(true)
    setError(null)

    try {
      const data = await readExcelFile(file)
      setExcelData(data)
    } catch (err) {
      setError(err.message)
      console.error('Error processing file:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const handleReset = () => {
    setExcelData(null)
    setError(null)
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>📊 Analizador de Excel</h1>
        <p>Carga, analiza y visualiza tus archivos Excel de forma fácil y rápida</p>
      </header>

      <main className="app-main">
        {error && (
          <div className="error-message">
            <p>❌ {error}</p>
            <button onClick={handleReset}>Intentar de nuevo</button>
          </div>
        )}

        {!excelData ? (
          <FileUploader
            onFileUpload={handleFileUpload}
            isLoading={isLoading}
          />
        ) : (
          <div className="data-section">
            <div className="data-header">
              <button className="reset-button" onClick={handleReset}>
                ← Cargar otro archivo
              </button>
            </div>
            <DataViewer excelData={excelData} />
          </div>
        )}
      </main>
    </div>
  )
}

export default App

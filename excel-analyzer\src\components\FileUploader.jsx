import React, { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileSpreadsheet, AlertCircle } from 'lucide-react';

const FileUploader = ({ onFileUpload, isLoading }) => {
  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {
    if (rejectedFiles.length > 0) {
      alert('Por favor, selecciona solo archivos Excel (.xlsx, .xls)');
      return;
    }

    if (acceptedFiles.length > 0) {
      onFileUpload(acceptedFiles[0]);
    }
  }, [onFileUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls']
    },
    multiple: false,
    disabled: isLoading
  });

  return (
    <div className="file-uploader">
      <div
        {...getRootProps()}
        className={`dropzone ${isDragActive ? 'active' : ''} ${isLoading ? 'disabled' : ''}`}
      >
        <input {...getInputProps()} />
        <div className="dropzone-content">
          {isLoading ? (
            <>
              <div className="spinner"></div>
              <p>Procesando archivo...</p>
            </>
          ) : (
            <>
              {isDragActive ? (
                <>
                  <Upload size={48} className="upload-icon active" />
                  <p>Suelta el archivo aquí...</p>
                </>
              ) : (
                <>
                  <FileSpreadsheet size={48} className="upload-icon" />
                  <p>Arrastra y suelta un archivo Excel aquí</p>
                  <p className="or-text">o</p>
                  <button className="upload-button">Seleccionar archivo</button>
                  <div className="file-types">
                    <AlertCircle size={16} />
                    <span>Formatos soportados: .xlsx, .xls</span>
                  </div>
                </>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default FileUploader;
